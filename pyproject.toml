[project]
name = "droidrun"
version = "0.3.2"
description = "A framework for controlling Android devices through LLM agents"
authors = [
    {name = "<PERSON><PERSON>", email = "<EMAIL>"},
]
dependencies = [
    "click>=8.1.0",
    "rich>=13.0.0",
    "pydantic>=2.0.0",
    "aiofiles>=23.0.0",
    "openai>=1.0.0",
    "anthropic>=0.7.0",
    "pillow>=10.0.0",
    "python-dotenv>=1.0.0",
    "llama-index",
    "llama-index-callbacks-arize-phoenix",
    "arize-phoenix",
    "llama-index-llms-openai",
    "llama-index-llms-openai-like",
    "llama-index-llms-google-genai",
    "llama-index-llms-deepseek",
    "llama-index-llms-anthropic",
    "llama-index-llms-ollama",
    "posthog==6.0.2",
    "typing_extensions",
    "llama-index-llms-litellm>=0.5.1",
    "llama-index-llms-dashscope>=0.4.1",
]
requires-python = ">=3.10"
readme = "README.md"
license = {text = "MIT"}
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Developers",
    "Intended Audience :: Information Technology",
    "Intended Audience :: Science/Research",
    "License :: OSI Approved :: MIT License",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Topic :: Software Development :: Libraries :: Python Modules",
    "Topic :: Software Development :: Testing",
    "Topic :: Scientific/Engineering :: Artificial Intelligence",
    "Topic :: Software Development :: Libraries :: Application Frameworks",
    "Topic :: Communications :: Chat",
    "Topic :: Software Development :: Testing :: Acceptance",
    "Topic :: Software Development :: Quality Assurance",
    "Topic :: System :: Emulators",
    "Topic :: Utilities",
]

[project.urls]
Homepage = "https://github.com/droidrun/droidrun"
"Bug Tracker" = "https://github.com/droidrun/droidrun/issues"
Documentation = "https://docs.droidrun.ai/"

[project.optional-dependencies]
dev = [
    "black>=23.0.0",
    "ruff>=0.1.0",
    "mypy>=1.0.0",
    "bandit>=1.7.0",
    "safety>=2.0.0"
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.build.targets.wheel]
packages = ["droidrun"]

[project.scripts]
droidrun = "droidrun.cli.main:cli"

[tool.ruff]
line-length = 100
target-version = "py310"

[tool.ruff.lint]
select = [
    "E",  # pycodestyle errors
    "W",  # pycodestyle warnings
    "F",  # pyflakes
    "I",  # isort
    "B",  # flake8-bugbear
] 

[[tool.pydoc-markdown.loaders]]
type = "python"
packages = [ "droidrun" ]
modules = [ "droidrun" ]

[[tool.pydoc-markdown.processors]]
type = "filter"
documented_only = false
do_not_filter_modules = true
skip_empty_modules = true

[[tool.pydoc-markdown.processors]]
type = "smart"

[[tool.pydoc-markdown.processors]]
type = "crossref"

[tool.pydoc-markdown.renderer]
type = "hugo"
build_directory = "docs"
content_directory = "v3/sdk"
clean_render = true


[tool.pydoc-markdown.renderer.markdown]
classdef_with_decorators = false
signature_with_decorators = false
data_code_block = true
descriptive_class_title = false
render_module_header = false
format_code_style = "facebook"
add_module_prefix = false
add_method_class_prefix = true

header_level_by_type = {Module = 1, Class = 2, Method = 4, Function = 4, Variable = 4}


[[tool.pydoc-markdown.renderer.pages]]
title = "DroidAgent"
name = "droid-agent"
contents = [ 
    "droidrun.agent.droid.droid_agent.DroidAgent",
    "droidrun.agent.droid.droid_agent.DroidAgent.__init__",
    "droidrun.agent.droid.droid_agent.DroidAgent.run",
]

[[tool.pydoc-markdown.renderer.pages]]
title = "Tools"
name = "base-tools"
contents = [
    "droidrun.tools.tools.Tools.*",
    "droidrun.tools.tools.describe_tools",
]

[[tool.pydoc-markdown.renderer.pages]]
title = "AdbTools"
name = "adb-tools"
contents = [
    "droidrun.tools.adb.AdbTools.*",
]

[[tool.pydoc-markdown.renderer.pages]]
title = "IOSTools"
name = "ios-tools"
contents = [
    "droidrun.tools.ios.IOSTools.*",
]

[[tool.pydoc-markdown.renderer.pages]]
title = "Adb Utils"
name = "adb-utils"
contents = [
    "droidrun.adb.manager.DeviceManager.*",
    "droidrun.adb.device.Device.*",
]
