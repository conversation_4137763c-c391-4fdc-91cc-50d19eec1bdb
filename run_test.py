#!/usr/bin/env python3
"""
DroidRun测试示例脚本
"""

import asyncio
from droidrun import DroidAgent, AdbTools, set_language, Language
from llama_index.llms.google_genai import GoogleGenAI
from llama_index.llms.litellm import LiteLLM
import os
from llama_index.llms.dashscope import DashScope

async def run_ai_agent():
    """运行AI代理"""
    try:

        # 设置中文模式
        set_language(Language.CHINESE)
        # 创建工具
        tools = await AdbTools.create()
        print("设备连接成功")

        # 初始化LLM
        # llm = GoogleGenAI(
        #     model="gemini-2.5-flash",  # or "gemini-2.5-pro"  "gemini-2.5-flash "for enhanced reasoning
        #     api_key="AIzaSyB_qj1RZuW3A2tj5QR1yRsFjOQshAV7Qoo"
        # )
        
        # messages = [{"role": "user", "content": "你是谁？"}]
        os.environ["DASHSCOPE_API_KEY"] = "sk-9b3b003f53964a5292e8c56dc2073685"

        llm = DashScope(
            # 若没有配置环境变量，请用阿里云百炼API Key将下行替换为：api_key = "sk-xxx",
            api_key=os.getenv("DASHSCOPE_API_KEY"),
            # 可按需更换为其它深度思考模型
            model="qwen3-235b-a22b",
            # messages=messages,
            result_format="message", # Qwen3开源版模型只支持设定为"message"；为了更好的体验，其它模型也推荐您优先设定为"message"
            # 开启深度思考，该参数对qwen3-30b-a3b-thinking-2507、qwen3-235b-a22b-thinking-2507、QwQ、DeepSeek-R1 模型无效
            # enable_thinking=True,
            # stream=True,
            # incremental_output=True, # Qwen3开源版模型只支持 true；为了更好的体验，其它模型也推荐您优先设定为 true
        )

        llm = LiteLLM(
            model="openai/claude-4-sonnet",  #代理服务支持的模型名：必须claude4模型才行！claude3.7不支持图像
            api_key="sk-9b3b003f53964a5292e8c56dc2073685",
            api_base="https://d106f995v5mndm.cloudfront.net/v1",  # 代理服务地址
        )
        
        # llm = LiteLLM(
        #     model="openai/claude-4-sonnet",  #代理服务支持的模型名：必须claude4模型才行！claude3.7不支持图像
        #     api_key="br-L89gsGCZn4PfDBMDGEbgXrhyAs5Z7",
        #     api_base="https://d106f995v5mndm.cloudfront.net/v1",  # 代理服务地址
        # )
        
        print("LLM初始化成功")
        
        # 创建AI代理
        agent = DroidAgent(
            # goal="在当前浏览器页面，找到搜索框，输入中文'音乐'，然后点击搜索按钮进行搜索",
            # goal="在当前页面，点击'+',输入120",
            # goal="打开音乐app，在首页-顶部输入框，输入'刀郎'的歌，播放第一首歌 ",
            goal="在当前页面，使用拖拽操作将第3首歌曲拖动到第2首歌上面，最后点击'完成'",
            # goal="打开音乐app,进入我的页面，找到'自建歌单2'和'我的歌单1'列表（找到后可再往下滑动一点，直到能完整看到'我的歌单1'列表），点击'我的歌单1'列表，,点击页面-右上角-更多，在弹出的列表框中选择'排序方式',在弹出的选择排序方式对话框选择'自定义排序',进入自定义排序界面，使用拖拽操作将第3首歌曲拖动到第2首歌上面，最后点击'完成',预期结果：点击完成后，返回歌单列表界面，歌单列表中的歌曲顺序为刚才拖动后的顺序",
            # goal="在当前自定义排序界面，按住第3首歌曲，拖动第3首歌曲，到第2首歌上面，最后点击'完成',预期结果：,点击完成后，返回歌单列表界面，歌单列表中的歌曲顺序为刚才拖动后的顺序",
            llm=llm,
            tools=tools,
            max_steps=20,
            vision=True,
            reasoning=True,
            debug=True
        )
        
        print("开始执行AI代理任务...")
        # 执行任务
        result = await agent.run()
        print("\n" + "=" * 50)
        print("AI代理执行完成")
        print("=" * 50)
        print(f"执行结果: {result}")
        return True
    except Exception as e:
        print(f"AI代理执行失败: {e}")
        return False

if __name__ == "__main__":
    # 运行主程序
    asyncio.run(run_ai_agent())
